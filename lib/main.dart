import 'dart:io';

import 'package:dio/adapter.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:guests/constants.dart';
import 'package:guests/generated/locales.g.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:package_info/package_info.dart';
import 'package:sizer/sizer.dart';
import 'app/providers/account_provider.dart';
import 'app/providers/api_provider.dart';
import 'app/providers/box_provider.dart';
import 'app/providers/invoice_provider.dart';
import 'app/providers/order_provider.dart';
import 'app/providers/pref_provider.dart';
import 'app/routes/app_pages.dart';

void main() {
  // it should be the first line in main method
  WidgetsFlutterBinding.ensureInitialized();
  // AWAIT SERVICES INITIALIZATION.
  _initServices().then((value) => _runApp());
}

void _runApp() {
  runApp(
    LayoutBuilder(builder: (context, constraints) {
      return OrientationBuilder(builder: (context, orientation) {
        SizerUtil().init(constraints, orientation);
        return GetMaterialApp(
          debugShowCheckedModeBanner: false,
          title: "Application",
          initialRoute: AppPages.INITIAL,
          getPages: AppPages.routes,
          theme: ThemeData(
            primaryColor: kColorPrimary,
            accentColor: kColorAccent,
          ),
          translationsKeys: AppTranslation.translations,
          locale: Get.deviceLocale, // 将会按照此处指定的语言翻译
          fallbackLocale: Locale('en'), // 添加一个回调语言选项，以备上面指定的语言翻译不存在
          builder: (context, widget) {
            return GestureDetector(
              child: widget,
              onTap: () {
                Get.focusScope?.unfocus();
              },
            );
          },
        );
      });
    }),
  );
}

Future<void> _initServices() async {
  print('starting services ...');
  final data =
      await PlatformAssetBundle().load('assets/ca/lets-encrypt-r3.pem');
  SecurityContext.defaultContext
      .setTrustedCertificatesBytes(data.buffer.asUint8List());
  await Hive.initFlutter();
  await GetStorage.init();
  await Get.putAsync(
    () => PackageInfo.fromPlatform(),
    permanent: true,
  );
  Get.lazyPut(
    () {
      final dio = Dio();
      // 忽略憑證錯誤
      (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
          (HttpClient client) {
        client.badCertificateCallback =
            (X509Certificate cert, String host, int port) => true;
        return client;
      };
      return dio;
    },
    fenix: true,
  );
  final userDefault = await Hive.openBox(kKeySettings);
  Get.lazyPut(
    () => BoxProvider(userDefault, logger: kLogger),
    fenix: true,
  );
  Get.lazyPut(
    () => PrefProvider(
      packageInfo: Get.find(),
      boxProvider: Get.find(),
    ),
    fenix: true,
  );
  Get.lazyPut(
    () => ApiProvider(
      dio: Get.find<Dio>(),
      prefProvider: Get.find(),
    ),
    fenix: true,
  );
  Get.lazyPut(
    () => InvoiceProvider(
      dio: Get.find<Dio>(),
      apiKey: Get.find<PrefProvider>().invoiceApiKey,
      baseUrl: Get.find<PrefProvider>().invoiceApiUrl,
      soapUrl: Get.find<PrefProvider>().invoiceSoapUrl,
      posBAN: kPosBAN,
    ),
    fenix: true,
  );
  Get.lazyPut(() => OrderProvider(apiProvider: Get.find()), fenix: true);
  Get.lazyPut(() => AccountProvider(apiProvider: Get.find()), fenix: true);
}
