import 'package:flutter/material.dart';
import 'package:guests/constants.dart';

class ClippedBackground extends StatelessWidget {
  final Widget child;
  final bool clipped;

  const ClippedBackground({
    Key key,
    this.child,
    this.clipped = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (clipped == true) {
      return ClipRRect(
        borderRadius: kTopRadius,
        child: _child(),
      );
    }
    return _child();
  }

  Widget _child() {
    return SizedBox.expand(
      child: DecoratedBox(
        decoration: const BoxDecoration(
          borderRadius: kTopRadius,
          color: kColorBackground,
        ),
        child: child,
      ),
    );
  }
}
