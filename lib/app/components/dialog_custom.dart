import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/yes_no_button.dart';
import 'package:guests/app/components/rounded_button.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';
import 'package:guests/enums.dart';
import 'package:guests/ok_colors.dart';

enum DialogIcon {
  none,
  alert,
  question,
}

class DialogCustom extends StatelessWidget {
  final Widget footer;
  final Widget child;
  final Widget header;
  final DialogIcon icon;

  const DialogCustom({
    Key key,
    this.header,
    this.child,
    this.footer,
    this.icon = DialogIcon.none,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(20.0),
      child: Container(
        // width: 300.dw,
        decoration: const BoxDecoration(
          color: Colors.white,
          // borderRadius: BorderRadius.circular(20.0),
          boxShadow: [
            BoxShadow(
              color: Color(0x4d000000),
              offset: Offset(0, 0),
              blurRadius: 6,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: _children().toList(growable: false),
        ),
      ),
    );
  }

  Iterable<Widget> _children() sync* {
    if (header != null) {
      yield Container(
        height: 58.0,
        alignment: Alignment.center,
        decoration: const BoxDecoration(
          // borderRadius: BorderRadius.vertical(
          //   top: Radius.circular(20.0),
          // ),
          color: Colors.white,
          boxShadow: const [
            const BoxShadow(
              color: Color(0x4d000000),
              offset: Offset(0.0, 0.0),
              blurRadius: 6.0,
            ),
          ],
        ),
        child: header,
      );
    }
    yield const SizedBox(height: Constants.paddingVertical);
    var needSeparator = false;
    if (icon != null && icon != DialogIcon.none) {
      if (icon == DialogIcon.alert) {
        yield const Icon(
          Icons.error_outline,
          color: kColorPrimary,
          size: 80.0,
        );
        needSeparator = true;
      } else if (icon == DialogIcon.question) {
        yield const Icon(
          Icons.help_outline,
          color: kColorPrimary,
          size: 80.0,
        );
        needSeparator = true;
      }
    }
    if (child != null) {
      if (needSeparator == true) {
        yield const SizedBox(height: Constants.paddingVertical);
      }
      yield child;
    }
    yield const SizedBox(height: 12);
    if (footer != null) {
      yield Container(
        height: 46,
        padding: const EdgeInsets.all(3.0),
        decoration: const BoxDecoration(
          // borderRadius: BorderRadius.vertical(
          //   bottom: Radius.circular(20.0),
          // ),
          color: Color(0xffeeeef3),
        ),
        child: SizedBox(
          height: 40,
          child: footer,
        ),
      );
    }
  }

  // compare: static & factory & constructor
  // factory: 工廠模式，可回傳已產生的物件
  // static: 擺在 class 裏的全域 function
  // constructor: 變化建構參數的方式
  // static DialogCustom okayDialog({
  // DialogCustom.okayDialog({
  // factory DialogCustom.okayDialog({
  //   String titleText,
  //   String contentText,
  //   String buttonText = '確認',
  // }) {
  //   return DialogCustom.alert(
  //     titleText: titleText,
  //     contentText: contentText,
  //     buttonText: buttonText,
  //     icon: DIalogIcon.alert,
  //   );
  // }

  DialogCustom.alert({
    String titleText,
    String contentText,
    String buttonText = '確認',
    this.icon = DialogIcon.alert,
  })  : header = titleText == null ? null : _title(titleText),
        child = contentText == null ? null : _content(contentText),
        footer = RoundedButton(
          text: buttonText,
          onPressed: () => Get.back(),
        ),
        super();

  static Future<T> show<T>({
    final Widget header,
    final Widget child,
    final Widget footer,
  }) {
    return DialogCustom(
      header: header,
      child: child,
      footer: footer,
    ).dialog();
  }

  static Widget _title(String text) {
    return Text(
      text ?? '',
      style: const TextStyle(
        fontSize: 20.0,
        color: kColorTitleText,
        fontWeight: FontWeight.w700,
      ),
      textAlign: TextAlign.center,
    );
  }

  static Widget _content(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 26.0,
        // vertical: 20.0,
      ),
      child: Text(
        text ?? '',
        style: const TextStyle(
          fontSize: 16,
          color: OkColors.gray66,
        ),
        textAlign: TextAlign.left,
      ),
    );
  }

  static Widget yesNoButtons({
    final String leftText = '取消',
    final String rightText = '確認',
  }) {
    return YesNoButton(
      leftText: leftText,
      rightText: rightText,
      onLeftPressed: () => Get.back<Button>(result: Button.Negative),
      onRightPressed: () => Get.back<Button>(result: Button.Positive),
    );
  }

  static Future<Button> showAlert({
    String titleText,
    String contentText,
    String buttonText = '確認',
    DialogIcon icon = DialogIcon.alert,
  }) {
    return DialogCustom.alert(
      titleText: titleText,
      contentText: contentText,
      buttonText: buttonText,
      icon: icon,
    ).dialog();
  }

  DialogCustom.confirm({
    String titleText,
    String contentText = '',
    String leftButtonText = '取消',
    String rightButtonText = '確認',
    Widget content,
    this.icon = DialogIcon.none,
  })  : header = titleText == null ? null : _title(titleText),
        child = content != null
            ? content
            : (contentText == null ? null : _content(contentText)),
        footer = YesNoButton(
          leftText: leftButtonText,
          rightText: rightButtonText,
          onLeftPressed: () => Get.back<Button>(result: Button.Negative),
          onRightPressed: () => Get.back<Button>(result: Button.Positive),
        ),
        super();

  static Future<Button> showConfirm({
    String titleText,
    String contentText = '',
    String leftButtonText = '取消',
    String rightButtonText = '確認',
    Widget content,
    DialogIcon icon = DialogIcon.question,
  }) {
    return DialogCustom.confirm(
      titleText: titleText,
      contentText: contentText,
      leftButtonText: leftButtonText,
      rightButtonText: rightButtonText,
      icon: icon,
      content: content,
    ).dialog<Button>();
  }
}
