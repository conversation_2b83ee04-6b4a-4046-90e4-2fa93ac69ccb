import 'package:flutter/material.dart';
import 'package:flutter_svg_provider/flutter_svg_provider.dart';
import 'package:guests/app/models/store_account_model.dart';
import 'package:guests/extension.dart';

class Avatar extends StatelessWidget {
  final StoreAccount data;
  final bool showStatus;

  const Avatar({
    Key key,
    @required this.data,
    this.showStatus = true,
  }) : super(key: key);

  Iterable<Widget> _items() sync* {
    yield _avatar();
    yield SizedBox(width: 12.0);
    yield _name();
    yield SizedBox(width: 12.0);
    if (showStatus == true) {
      yield _status();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(children: _items().toList());
  }

  Widget _avatar() {
    return Container(
      width: 60.0,
      height: 60.0,
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        image: DecorationImage(
          image: Svg('assets/images/avatar.svg'),
        ),
        boxShadow: const [
          const BoxShadow(
            color: const Color(0x4dc68329),
            offset: const Offset(3.0, 3.0),
            blurRadius: 12.0,
          ),
        ],
      ),
    );
  }

  Widget _name() {
    final children = <Widget>[];
    children.add(
      Text(
        data?.role?.name ?? '',
        style: const TextStyle(
          fontSize: 15,
          color: const Color(0xff936230),
          height: 1.0,
        ),
        textAlign: TextAlign.left,
      ),
    );
    children.add(SizedBox(height: 8.0));
    children.add(
      Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            this.data?.name ?? '',
            style: const TextStyle(
              fontSize: 22,
              color: Colors.black,
              fontWeight: FontWeight.w700,
              height: 1.0,
            ),
            textAlign: TextAlign.left,
          ),
          const SizedBox(
            width: 8.0,
          ),
          Text(
            data?.comment ?? '',
            style: const TextStyle(
              fontSize: 16,
              color: const Color(0xff5c4c4c),
              height: 1.0,
            ),
            textAlign: TextAlign.left,
          ),
        ],
      ),
    );
    return Expanded(
      child: Align(
        alignment: Alignment.centerLeft,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: children,
        ),
      ),
    );
  }

  Widget _status() {
    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(
        horizontal: 6.0,
        vertical: 2.0,
      ),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(const Radius.circular(5.0)),
        color: this.data?.displayStatusColor ?? const Color(0xffb9b9b9),
      ),
      child: Text(
        data?.displayStatus ?? '',
        style: const TextStyle(
          fontSize: 14,
          color: Colors.white,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
