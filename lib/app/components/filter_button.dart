import 'package:flutter/material.dart';

class FilterButton extends StatelessWidget {
  final Function onPressed;
  final String buttonText;
  final Color textColor;
  final Widget child;
  final EdgeInsetsGeometry padding;
  final Color backgroundColor;
  final BorderSide side;

  const FilterButton({
    Key key,
    this.onPressed,
    this.buttonText = '篩選',
    this.child,
    this.textColor,
    this.padding,
    this.backgroundColor,
    this.side,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: TextButton(
        onPressed: onPressed,
        child: child ??
            Text(
              buttonText ?? '',
              style: TextStyle(
                fontSize: 16,
                color: textColor ?? Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
        style: TextButton.styleFrom(
          side: side ??
              BorderSide(
                width: 1.0,
                color: Colors.white,
              ),
          shape: const StadiumBorder(),
          minimumSize: Size.zero,
          backgroundColor: backgroundColor ?? Colors.transparent,
          padding: padding ??
              EdgeInsets.symmetric(
                vertical: 8,
                horizontal: 16,
              ),
        ),
      ),
    );
  }
}
