///
/// 單選 dialog
///
import 'package:flutter/material.dart';
import 'package:get/get.dart';

typedef ValueGetter2<T, R> = R Function(T value);

class DialogActions extends StatelessWidget {
  final String titleText;
  final List<String> actions;

  const DialogActions({
    Key key,
    this.titleText,
    this.actions,
  }) : super(key: key);

  ///
  /// 返回 index
  ///
  static Future<T> show<T>({
    String titleText,
    List<String> actions,
  }) {
    return Get.dialog<T>(
      DialogActions(
        titleText: titleText,
        actions: actions,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SimpleDialog(
      title: Text(this.titleText ?? ''),
      titleTextStyle: const TextStyle(
        fontSize: 20,
        color: const Color(0xff333333),
        fontWeight: FontWeight.w700,
      ),
      children: List.generate(actions.length, (index) {
        return SimpleDialogOption(
          onPressed: () => Get.back<num>(result: index),
          child: Text(actions.elementAt(index)),
        );
      }),
    );
  }
}
