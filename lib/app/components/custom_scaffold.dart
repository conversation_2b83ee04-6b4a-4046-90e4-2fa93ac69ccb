import 'package:flutter/material.dart';
import 'package:guests/app/components/clipped_background.dart';
import 'package:guests/app/components/custom_app_bar.dart';
import 'package:guests/app/components/primary_background.dart';

class CustomScaffold extends StatelessWidget {
  final Widget title;
  final Widget child;
  final String titleText;
  final bool clipped;
  final Iterable<Widget> actions;

  const CustomScaffold({
    Key key,
    this.child,
    this.title,
    this.titleText = '',
    this.actions,
    this.clipped = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PrimaryBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: CustomAppBar(
          titleText,
          title: title,
          actions: actions,
        ),
        body: ClippedBackground(
          child: child,
          clipped: clipped,
        ),
      ),
    );
  }
}
