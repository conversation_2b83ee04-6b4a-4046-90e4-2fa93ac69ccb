import 'package:flutter/material.dart';

class YesNoButton extends StatelessWidget {
  final String leftText;
  final String rightText;
  final VoidCallback onLeftPressed;
  final VoidCallback onRightPressed;

  const YesNoButton({
    Key key,
    this.leftText = '取消',
    this.rightText = '確認',
    this.onLeftPressed,
    this.onRightPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      child: Row(
        children: [
          Expanded(child: _leftButton()),
          Expanded(child: _rightButton()),
        ],
      ),
    );
  }

  Widget _rightButton() {
    return DecoratedBox(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.horizontal(
          right: Radius.circular(25.0),
        ),
        gradient: LinearGradient(
          begin: Alignment(-1.0, 0.0),
          end: Alignment(1.0, 0.0),
          colors: [
            Color(0xfff89328),
            Color(0xffe66f53),
          ],
          stops: [0.0, 1.0],
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0x29000000),
            offset: Offset(0, 0),
            blurRadius: 10,
          ),
        ],
      ),
      child: TextButton(
        onPressed: onRightPressed,
        child: Text(
          rightText ?? '確認',
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _leftButton() {
    return DecoratedBox(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.horizontal(
          left: Radius.circular(25.0),
        ),
        color: const Color(0xff3e4b5a),
        boxShadow: [
          BoxShadow(
            color: const Color(0x29000000),
            offset: Offset(0, 0),
            blurRadius: 10,
          ),
        ],
      ),
      child: TextButton(
        // style: TextButton.styleFrom(
        //   minimumSize: Size.fromHeight(40),
        //   padding: EdgeInsets.zero,
        //   backgroundColor: const Color(0xff3e4b5a),
        //   shape: RoundedRectangleBorder(
        //     borderRadius: BorderRadius.horizontal(
        //       left: Radius.circular(25.0),
        //     ),
        //   ),
        //   shadowColor: const Color(0x29000000),
        // ),
        onPressed: onLeftPressed,
        child: Text(
          leftText ?? '取消',
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
