import 'package:flutter/material.dart';
import 'package:guests/constants.dart';

class CustomEditor extends StatelessWidget {
  final Color labelColor;
  final String labelText;
  final String hintText;
  final String initialValue;
  final ValueChanged<String> onChanged;
  final ValueChanged<String> validator;
  final double horizontalPadding;
  final bool obscureText;
  final TextStyle style;
  final TextInputType keyboardType;
  final bool readonly;
  final bool enable;

  const CustomEditor({
    Key key,
    this.keyboardType,
    this.style,
    this.labelColor,
    this.labelText,
    this.hintText,
    this.initialValue,
    this.onChanged,
    this.validator,
    this.readonly,
    this.enable,
    this.horizontalPadding = kPadding,
    this.obscureText = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: this.horizontalPadding,
      ),
      child: TextFormField(
        enabled: this.enable ?? true,
        readOnly: this.readonly ?? false,
        keyboardType: this.keyboardType,
        style: this.style ??
            const TextStyle(
              fontSize: 20.0,
            ),
        obscureText: this.obscureText,
        validator: this.validator,
        initialValue: this.initialValue,
        onChanged: this.onChanged,
        decoration: InputDecoration(
          labelText: this.labelText ?? '',
          labelStyle: TextStyle(
            fontSize: 16.0,
            color: this.labelColor ?? Colors.black,
          ),
          prefixStyle: const TextStyle(
            fontSize: 16.0,
            color: Colors.black,
          ),
          hintText: this.hintText ?? '',
          hintStyle: const TextStyle(
            fontSize: 16.0,
            color: kColorContentText,
          ),
          suffixStyle: const TextStyle(
            fontSize: 16.0,
            color: kColorContentText,
          ),
        ),
      ),
    );
  }
}
