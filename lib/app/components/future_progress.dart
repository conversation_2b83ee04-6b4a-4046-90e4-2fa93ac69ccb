import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/dialog_custom.dart';
import 'package:guests/app/components/progressing.dart';
import 'package:guests/extension.dart';

class FutureProgress<T> extends StatelessWidget {
  final Widget Function(T t) onData;
  final Widget Function(Object error) onError;
  final WidgetBuilder onLoading;
  final Widget onEmpty;
  final _value = Rx<T>(null);
  T get value => _value.value;

  final _status = RxStatus.loading().obs;
  RxStatus get status => _status.value;

  FutureProgress({
    Key key,
    @required Future<T> future,
    this.onData,
    this.onError,
    this.onLoading,
    this.onEmpty,
  }) : super(key: key) {
    future?.then((value) {
      _value.value = value;
      _status.value = RxStatus.success();
    }, onError: (error) {
      _status.value = RxStatus.error(error.toString());
    });
  }

  static Future<T> show<T>({
    Future<T> future,
    Widget Function(T t) onData,
    Widget Function(Object error) onError,
    final WidgetBuilder onLoading,
    final Widget onEmpty,
  }) {
    return FutureProgress<T>(
      future: future,
      onData: onData,
      onError: onError,
      onLoading: onLoading,
      onEmpty: onEmpty,
    ).dialog(barrierDismissible: false);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (status.isLoading) {
        return onLoading?.call(context) ?? Progressing();
      }
      if (status.isError) {
        return onError != null
            ? onError(status.errorMessage)
            : DialogCustom.alert(
                contentText: status.errorMessage,
              );
      }
      if (status.isEmpty) {
        return onEmpty ?? const SizedBox();
      }
      if (status.isSuccess) {
        final dataWidget = onData?.call(value);
        if (dataWidget != null) return dataWidget;
        1.seconds.delay(() => Get.back<T>(result: value));
        return const Progressing.check();
      }
      return const SizedBox();
    });
  }
}
