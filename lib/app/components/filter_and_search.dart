import 'package:flutter/material.dart';
import 'package:guests/constants.dart';

class FilterAndSearch extends StatelessWidget {
  final Iterable<String> tabs;
  final ValueChanged<int> onPressed;
  final ValueChanged<String> onChanged;
  final String hintText;

  const FilterAndSearch({
    Key key,
    this.tabs = const ['今日', '全部'],
    this.onPressed,
    this.onChanged,
    this.hintText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: kBorderRadius,
            child: Container(
              // decoration: BoxDecoration(
              //   borderRadius: BorderRadius.circular(kRadius),
              //   color: Colors.white,
              // ),
              color: Colors.white,
              width: 140.0,
              height: kButtonHeight,
              child: DefaultTabController(
                length: this.tabs.length,
                child: TabBar(
                  onTap: this.onPressed,
                  indicator: BoxDecoration(
                    color: kColorPrimary,
                  ),
                  // indicatorColor: kColorPrimary,
                  unselectedLabelColor: Colors.black,
                  unselectedLabelStyle: const TextStyle(
                    fontSize: 16,
                  ),
                  labelColor: Colors.white,
                  labelStyle: const TextStyle(
                    fontSize: 16,
                  ),
                  tabs: List.generate(
                    this.tabs.length,
                    (index) {
                      return Tab(
                        text: this.tabs.elementAt(index),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(
            width: 8.0,
          ),
          Expanded(
            child: TextFormField(
              onChanged: this.onChanged,
              decoration: InputDecoration(
                contentPadding: EdgeInsets.zero,
                filled: true,
                fillColor: Colors.white,
                border: const OutlineInputBorder(
                  borderSide: BorderSide.none,
                  borderRadius: kBorderRadius,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: const Color(0xFFD3D8DC),
                ),
                hintText: this.hintText ?? '請輸入關鍵字',
                hintStyle: const TextStyle(
                  fontSize: 16,
                  color: const Color(0xff666666),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
