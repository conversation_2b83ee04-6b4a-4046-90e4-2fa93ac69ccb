import 'package:flutter/material.dart';
import 'package:guests/app/components/CustomBackButton.dart';

class CustomAppBar extends AppBar {
  CustomAppBar(
    String titleText, {
    Key key,
    Widget title,
    Iterable<Widget> actions,
    Brightness brightness,
  }) : super(
          key: key,
          elevation: 0.0,
          backgroundColor: Colors.transparent,
          brightness: brightness ?? Brightness.dark,
          leading: const CustomBackButton(),
          actions: List.from(actions ?? [], growable: false),
          centerTitle: true,
          title: title ??
              Text(
                titleText ?? '',
                style: const TextStyle(
                  fontSize: 16.0,
                  color: Colors.white,
                ),
              ),
        );
}
