import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_sunmi_printer/flutter_sunmi_printer.dart';
import 'package:get/get.dart';
import 'package:guests/app/models/invoice_status_req.dart';
import 'package:guests/app/models/order_Invoice_req.dart';
import 'package:guests/app/models/order_creating_model.dart';
import 'package:guests/app/models/order_detail_model.dart';
import 'package:guests/app/providers/box_provider.dart';
import 'package:guests/app/providers/invoice_provider.dart';
import 'package:guests/app/providers/order_provider.dart';
import 'package:guests/app/providers/pref_provider.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';
import 'package:guests/enums.dart';
import 'package:guests/keys.dart';
import 'package:screenshot/screenshot.dart';

class CreateOrderController extends GetxController {
  final paidController = TextEditingController();
  // final inputController = TextEditingController();
  // 發票截圖
  final widgetUpdater = Completer();
  final screenshotController = ScreenshotController();
  final _draft = OrderCreating().obs;
  final OrderProvider orderProvider;
  final InvoiceProvider invoiceProvider;
  final PrefProvider prefProvider;
  final _id = ''.obs;
  final itemName = ''.obs;
  final _invoiceVisible = false.obs;
  bool get invoiceVisible => _invoiceVisible.value;
  final _orderDetail = Rx<OrderDetail>();

  String get id => _id.value;
  OrderDetail get orderDetail => _orderDetail.value;
  OrderCreating get draft => _draft.value;

  bool get invoiceEnabled {
    final condition1 = prefProvider.invoiceEnabled;
    final condition2 = draft.invoice ??= true;
    return condition1 && condition2;
  }

  set invoiceEnabled(bool value) {
    draft.invoice = value;
    refreshDraft();
  }

  ///
  /// 買方
  ///
  bool get containsBuyer {
    return draft.vatNumber != null && draft.vatNumber.isNotEmpty;
  }

  ///
  /// 取得稅率
  ///
  num get _taxRateNormal {
    return containsBuyer ? kTaxRateNormal : 0.0;
  }

  ///
  /// 稅率
  ///
  num get taxRate {
    final taxType = prefProvider.taxType;
    return taxType == 1 ? _taxRateNormal : kTaxRateFree;
  }

  BoxProvider get boxProvider => prefProvider.boxProvider;

  String get displayTotal {
    final ret = draft.calculateTotal;
    this.paidController.text ??= '';
    // if (this.paidController.text.isEmpty) {
    //   this.paidController.text = '$ret';
    //   this.orderCreating.value.paid = ret;
    // }
    return ret.currency;
  }

  CreateOrderController({
    @required this.orderProvider,
    @required this.invoiceProvider,
    @required this.prefProvider,
  });

  @override
  void onInit() {
    super.onInit();
    final condition1 = this.prefProvider.invoiceEnabled;
    final condition2 = !(this.prefProvider.invoiceSkipped);
    this.invoiceEnabled = condition1 && condition2;
    this.itemName.value = this.prefProvider.itemName;
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    //
  }

  Future<num> submit() async {
    final orderId = await _createOrder();
    if (invoiceEnabled && draft.total > 0) {
      // 建立發票
      final invoiceNumber = await _createInvoice(orderId);
      if (invoiceNumber != null && invoiceNumber.isNotEmpty) {
        // 列印發票
        await _print();
      } else {
        // TODO: timeout 時要檢查發票狀態
        // if (false == await _confirmInvoice(invoiceNumber)) {
        //   // TODO: log the error
        // }
      }
    }
    return orderId;
  }

  Future<bool> _confirmInvoice(String invoiceNumber) async {
    final time = orderDetail.createdDateTime;
    final req = InvoiceStatusReq(
      invoiceDate: time.yMd,
      invoiceNo: invoiceNumber,
    );
    final status = await invoiceProvider.getInvoiceStatus(req);
    return status.isInvoice;
  }

  Future<String> _createInvoice(num orderId) async {
    // 取得發票號碼
    final invoiceNumber = await invoiceProvider.getInvoiceNo(
      time: orderDetail.createdDateTime,
      seller: prefProvider.taxId,
    );
    if (invoiceNumber != null && invoiceNumber.isNotEmpty) {
      final randomNumber = InvoiceProvider.createRandomNumber();
      final req = OrderInvoiceReq(
        invoiceNumber: invoiceNumber,
        randomNumber: randomNumber,
        invoicePaper: true,
        vatNumber: draft.vatNumber,
        // carrierType:
        // carrierId:
        // npoBan:
      );
      // 建立訂單發票資訊
      orderId = await orderProvider.createInvoice(
        orderId,
        req,
      );
      if (orderId != null && orderId > 0) {
        // TODO: 全部使用佇列上傳
        _orderDetail.value = await orderProvider.getOrderDetail(id);
        // 開立發票
        draft.invoice = true;
        // 發票號碼
        draft.invoiceNumber = invoiceNumber;
        // 列印
        draft.invoicePaper = true;
        // 隨機碼
        draft.randomNumber = randomNumber;
        try {
          // 上傳發票
          await _syncInvoice(invoiceNumber, randomNumber);
        } catch (e) {
          // 上傳發票失敗，加入補上傳佇列
          final box = await boxProvider.getLazyBox(Keys.BoxOrderInvoice);
          final key = '$orderId.${BpscmInvoiceStatus.Invoice.index}';
          await box.put(key, invoiceNumber);
        }
      }
    }
    return invoiceNumber;
  }

  Future<num> _createOrder() async {
    // 確認 token
    // if (prefProvider.jwt.isExpired) {
    //   throw '登入逾時，請重新登入';
    // }
    // 確認賣方統一編號
    // if (invoiceEnabled) {
    //   final taxId = prefProvider.taxId ?? '';
    //   if (taxId.isEmpty) {
    //     throw '需設定賣方統編';
    //   }
    // }
    final orderId = await orderProvider.createOrder(draft);
    if (orderId > 0) {
      _id.value = '$orderId';
      _orderDetail.value = await orderProvider.getOrderDetail(id);
      // 取得時間
      // HACK:
      // final date = DateTime(2017, 12, 1, 18, 32);
      // final dateString = kDateFormat.format(date);
      // this.orderCreating.value.createdAt = dateString;
      draft.createdAt = orderDetail.createdAt;
      return orderId;
    }
    return 0;
  }

  Future<String> _syncInvoice(String invoiceNumber, String randomNumber) async {
    // 賣方
    final taxId = prefProvider.taxId ?? '';
    // 免稅、應稅、零稅率
    final taxType = prefProvider.taxType;
    // 發票時間
    final time = orderDetail.createdDateTime;
    // HACK: 測試上傳發票失敗
    // throw '上傳發票失敗: 字軌($invoiceNumber)';
    // 上傳發票
    final ls = await invoiceProvider.pushInvoice(
      invoiceNo: invoiceNumber,
      randomNumber: randomNumber,
      seller: taxId,
      buyer: draft.vatNumber,
      orderNo: orderDetail.orderNumber,
      invoiceDate: kDateTimeFormat.format(time),
      price: draft.total,
      itemName: itemName.value,
      taxType: taxType,
    );
    // 檢查
    if (ls.isNotEmpty) {
      final element = ls.first;
      if ('OK' == element.status) {
        return element.invoiceNo;
      }
      throw element.message;
    }
    throw '上傳發票失敗: 字軌($invoiceNumber)';
  }

  Future<void> _print() async {
    final invoice = orderDetail.toSunmiInvoice();
    // 建立訂單時，一定是首印
    invoice.printMarkup = false;
    invoice.items
        .where((element) => '現場購買' == element.name)
        .forEach((element) => element.name = itemName.value);
    await SunmiPrinter.printInvoice(invoice);
  }

  void refreshDraft() {
    _draft.refresh();
  }
}
