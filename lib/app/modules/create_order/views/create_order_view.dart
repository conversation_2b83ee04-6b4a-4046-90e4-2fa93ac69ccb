import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:get/get.dart';
import 'package:guests/app/components/custom_scaffold.dart';
import 'package:guests/app/components/dialog_custom.dart';
import 'package:guests/app/components/future_progress.dart';
import 'package:guests/app/components/invoice_page.dart';
import 'package:guests/app/components/BottomButton.dart';
import 'package:guests/constants.dart';
import 'package:guests/enums.dart';
import 'package:guests/extension.dart';
import 'package:screenshot/screenshot.dart';

import '../controllers/create_order_controller.dart';

class CreateOrderView extends GetView<CreateOrderController> {
  final GlobalKey _key = GlobalKey<FormState>();

  CreateOrderView({
    Key key,
  }) : super(key: key);

  Future<void> _submit() async {
    final form = _key.currentState as FormState;
    if (form != null && form.validate()) {
      bool needToProcess = true;
      if (controller.invoiceEnabled && controller.draft.total <= 0) {
        final button = await DialogCustom.showConfirm(
          titleText: '不開立發票',
          contentText: '結帳金額為0，不開立發票',
          rightButtonText: '繼續結帳',
        );
        if (button == null || button.isNegative) {
          needToProcess = false;
        }
      }
      if (needToProcess) {
        final orderId = await FutureProgress.show(
          future: controller.submit(),
        );
        if (orderId is num && orderId > 0) {
          Get.back(result: orderId);
        }
      }
    }
  }

  Widget _main() {
    final children = <Widget>[];
    // 發票截圖
    children.addIf(true, Obx(() {
      if (controller.invoiceVisible) {
        final ret = Screenshot(
          controller: controller.screenshotController,
          child: InvoicePage(
            controller.orderDetail.toInvoice(),
            storeName: controller.prefProvider.storeName,
            productName: controller.itemName.value,
          ),
        );
        controller.widgetUpdater.complete();
        return ret;
      }
      return SizedBox();
    }));
    // 國防布
    children.addIf(
      true,
      ColoredBox(
        color: kColorBackground,
        child: SizedBox.expand(),
      ),
    );
    // 使用者實際看到的預覽介面
    children.addIf(true, _slave());
    return Stack(
      alignment: Alignment.topCenter,
      children: children,
    );
  }

  Widget _slave() {
    return DecoratedBox(
      decoration: const BoxDecoration(
        borderRadius: kTopRadius,
        color: const Color(0xffEEEEF3),
        // color: const Color(0xFFDEDEE6),
      ),
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          SizedBox.expand(
            child: SingleChildScrollView(
              child: Form(
                key: _key,
                child: _Page(),
              ),
            ),
          ),
          BottomButton(
            '新增消費記錄',
            onPressed: _submit,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      titleText: 'OKpay',
      clipped: true,
      child: _main(),
    );
  }
}

class _Page extends GetView<CreateOrderController> {
  const _Page({
    Key key,
  }) : super(key: key);

  List<Widget> _list() {
    final children = <Widget>[];
    children.addIf(
      true,
      const SizedBox(
        height: 18.0,
      ),
    );
    children.addIf(
      true,
      _Editor(
        // initText: '芒果禮盒',
        initText: controller.itemName.value,
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
        ],
        hintText: '商品名稱',
        onChanged: controller.itemName,
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 4.0,
      ),
    );
    children.addIf(
      true,
      _ListTile(
        leading: _IconLabel(
          icon: 'assets/images/icon_pos_01.svg',
          text: '商品小計',
        ),
        trailing: _Editor(
          textAlign: TextAlign.end,
          inputFormatters: [
            FilteringTextInputFormatter.singleLineFormatter,
            FilteringTextInputFormatter.digitsOnly,
          ],
          keyboardType: TextInputType.number,
          onChanged: (value) {
            final _ = num.tryParse(value) ?? 0;
            controller.draft.subtotal = _;
            controller.refreshDraft();
          },
        ),
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 4.0,
      ),
    );
    children.addIf(
      true,
      Divider(
        indent: kPadding,
        endIndent: kPadding,
      ),
    );
    // TODO: revert me
    children.addIf(
      false,
      _ListTile(
        leading: _IconLabel(
          icon: 'assets/images/icon_pos_02.svg',
          text: '服務費(10%)',
        ),
        trailing: Text(
          '152',
          style: TextStyle(
            fontSize: 17,
            color: const Color(0xff333333),
          ),
          textAlign: TextAlign.right,
        ),
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 4.0,
      ),
    );
    // TODO: revert me
    children.addIf(
      false,
      Divider(
        indent: kPadding,
        endIndent: kPadding,
      ),
    );
    children.addIf(
      true,
      _ListTile(
        leading: _IconLabel(
          icon: 'assets/images/icon_pos_03.svg',
          text: '現場折價',
        ),
        title: Text(
          '－',
          style: TextStyle(
            fontSize: 16,
            color: const Color(0xff333333),
          ),
          textAlign: TextAlign.center,
        ).paddingSymmetric(
          horizontal: kDefaultPadding,
        ),
        trailing: _Editor(
          textAlign: TextAlign.end,
          inputFormatters: [
            FilteringTextInputFormatter.singleLineFormatter,
            FilteringTextInputFormatter.digitsOnly,
          ],
          keyboardType: TextInputType.number,
          onChanged: (value) {
            final _ = num.tryParse(value) ?? 0;
            controller.draft.discount = _;
            controller.refreshDraft();
          },
        ),
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 4.0,
      ),
    );
    children.addIf(
      true,
      Divider(
        indent: kPadding,
        endIndent: kPadding,
      ),
    );
    children.addIf(
      true,
      _ListTile(
        leading: _IconLabel(
          icon: 'assets/images/icon_pos_04.svg',
          text: '額外費用',
        ),
        title: Text(
          '＋',
          style: TextStyle(
            fontSize: 16,
            color: const Color(0xff333333),
          ),
          textAlign: TextAlign.center,
        ).paddingSymmetric(
          horizontal: kDefaultPadding,
        ),
        trailing: _Editor(
          textAlign: TextAlign.end,
          inputFormatters: [
            FilteringTextInputFormatter.singleLineFormatter,
            FilteringTextInputFormatter.digitsOnly,
          ],
          keyboardType: TextInputType.number,
          onChanged: (value) {
            final _ = num.tryParse(value) ?? 0;
            controller.draft.additionalCharges = _;
            controller.refreshDraft();
          },
        ),
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 4.0,
      ),
    );
    children.addIf(
      true,
      Divider(
        indent: kPadding,
        endIndent: kPadding,
      ),
    );
    children.addIf(
      true,
      _ListTile(
        leading: _IconLabel(
          icon: 'assets/images/icon_pos_05.svg',
          text: '商品總價',
        ),
        trailing: Obx(() {
          return Text(
            // '\$1670',
            '\$${controller.displayTotal}' ?? '',
            style: TextStyle(
              fontSize: 32,
              color: const Color(0xd9000000),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.right,
          );
        }),
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 4.0,
      ),
    );
    children.addIf(
      true,
      _ListTile(
        leading: _IconLabel(
          icon: 'assets/images/icon_pos_06.svg',
          text: '實收',
        ),
        trailing: _Editor(
          textAlign: TextAlign.end,
          inputFormatters: [
            FilteringTextInputFormatter.singleLineFormatter,
            FilteringTextInputFormatter.digitsOnly,
          ],
          // controller: controller.paidController,
          keyboardType: TextInputType.number,
          onChanged: (value) {
            final _ = num.tryParse(value) ?? 0;
            controller.draft.paid = _;
            controller.refreshDraft();
          },
        ),
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 8.0,
      ),
    );
    children.addIf(
      true,
      _ListTile(
        leading: _IconLabel(
          icon: 'assets/images/icon_pos_07.svg',
          text: '找零',
        ),
        trailing: Obx(() {
          return Text(
            // '\$30',
            '\$${controller.draft.displayChange}',
            style: const TextStyle(
              fontSize: 32,
              color: const Color(0xffe02020),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.right,
          );
        }),
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 4.0,
      ),
    );
    children.addIf(
      controller.prefProvider.invoiceEnabled,
      _Page01(),
    );
    children.addIf(
      true,
      const SizedBox(
        height: kBottomPadding,
      ),
    );
    return children;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _list(),
    );
  }
}

class _Page01 extends GetView<CreateOrderController> {
  List<Widget> _list() {
    final children = <Widget>[];
    children.addIf(
      true,
      const SizedBox(
        height: 20.0,
      ),
    );
    children.addIf(
      true,
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Obx(() {
              return _Editor(
                enabled: controller.invoiceEnabled,
                inputFormatters: [
                  FilteringTextInputFormatter.singleLineFormatter,
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(kCountInvoice),
                ],
                onChanged: (value) {
                  // 統一編號
                  if (GetUtils.hasMatch(value, kPatternInvoice)) {
                    controller.draft.vatNumber = value;
                  }
                },
                hintText: '買方統一編號',
                keyboardType: TextInputType.number,
                validator: (value) {
                  final _ = value ?? '';
                  if (_.isEmpty) {
                    return null;
                  }
                  if (GetUtils.hasMatch(_, kPatternInvoice)) {
                    return null;
                  }
                  if (controller.invoiceEnabled == false) {
                    return null;
                  }
                  return '請輸入8碼數字';
                },
              );
            }),
          ),
          const SizedBox(
            width: 12.0,
          ),
          Container(
            height: 54.0,
            alignment: Alignment.center,
            child: Text(
              '免開發票',
              style: TextStyle(
                fontFamily: 'Helvetica Neue',
                fontSize: 14,
                color: const Color(0xff6d7278),
              ),
              textAlign: TextAlign.right,
            ),
          ),
          const SizedBox(
            width: 8.0,
          ),
          Obx(() {
            return Container(
              height: 54.0,
              alignment: Alignment.center,
              child: Switch(
                value: !(controller.invoiceEnabled),
                onChanged: (bool value) {
                  controller.invoiceEnabled = !value;
                },
              ),
            );
          }),
        ],
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 4.0,
      ),
    );
    // children.addIf(
    //   true,
    //   Obx(() {
    //     final visialbe = this.controller.invoiceVisible.value;
    //     if (visialbe) {
    //       return ColoredBox(
    //         color: Colors.white,
    //         child: Screenshot(
    //           controller: controller.screenshotController,
    //           child: Container(
    //             color: Colors.white,
    //             width: 288.0,
    //             child: InvoicePage(
    //               json: controller.draft.toInvoiceJson(),
    //               storeName: controller.prefProvider.storeName,
    //               productName: controller.itemName.value,
    //               barcodeSvgString: '',
    //               leftQrSvgString: '',
    //               rightQrSvgString: '',
    //             ),
    //           ),
    //         ),
    //       );
    //     }
    //     return SizedBox();
    //   }),
    // );
    // children.addIf(true, InvoiceView());
    // TODO: revert me
    children.addIf(
      false,
      const SizedBox(
        height: 12.0,
      ),
    );
    // TODO: revert me
    children.addIf(
      false,
      _Editor(
        hintText: '請掃描載具/愛心碼',
        keyboardType: TextInputType.number,
      ).paddingSymmetric(
        horizontal: kPadding,
        vertical: 4.0,
      ),
    );
    children.addIf(
      true,
      const SizedBox(
        height: 20.0,
      ),
    );
    return children;
  }

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: const Color(0xffdedee6),
      child: Column(
        children: _list(),
      ),
    );
  }
}

class _ListTile extends StatelessWidget {
  final Widget leading;
  final Widget title;
  final Widget trailing;

  const _ListTile({
    Key key,
    this.leading,
    this.title,
    this.trailing,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
          child: Row(
            children: [
              Expanded(
                child: this.leading ?? SizedBox(),
              ),
              this.title ?? SizedBox(),
            ],
          ),
        ),
        Expanded(
          child: this.trailing ?? SizedBox(),
        ),
      ],
    );
  }
}

class _IconLabel extends StatelessWidget {
  final String icon;
  final String text;

  const _IconLabel({
    Key key,
    this.icon,
    this.text,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // SvgPicture.asset('assets/images/icon_pos_01.svg'),
        SvgPicture.asset(this.icon ?? ''),
        const SizedBox(
          width: 12.0,
        ),
        Text(
          // '商品小計',
          this.text ?? '',
          style: const TextStyle(
            fontSize: 14,
            color: const Color(0xff6d7278),
          ),
          textAlign: TextAlign.left,
        ),
      ],
    );
  }
}

// TODO: add max length property
class _Editor extends StatelessWidget {
  final String hintText;
  final String initText;
  final TextInputType keyboardType;
  final FormFieldValidator<String> validator;
  final ValueChanged<String> onChanged;
  final TextEditingController controller;
  final List<TextInputFormatter> inputFormatters;
  final TextAlign textAlign;
  final bool readonly;
  final bool enabled;

  const _Editor({
    Key key,
    this.textAlign,
    this.hintText,
    this.initText,
    this.validator,
    this.onChanged,
    this.controller,
    this.readonly,
    this.enabled,
    this.keyboardType,
    this.inputFormatters,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      initialValue: this.initText ?? '',
      enabled: this.enabled ?? true,
      readOnly: this.readonly ?? false,
      textAlign: this.textAlign ?? TextAlign.start,
      inputFormatters: this.inputFormatters,
      controller: this.controller,
      onChanged: this.onChanged,
      validator: this.validator,
      keyboardType: this.keyboardType ?? TextInputType.text,
      style: const TextStyle(
        fontSize: 24,
        color: Colors.black,
      ),
      decoration: InputDecoration(
        contentPadding: EdgeInsets.symmetric(
          horizontal: kPadding,
          vertical: 12.0,
        ),
        fillColor: Colors.white,
        filled: true,
        isDense: true,
        hintMaxLines: 1,
        hintStyle: const TextStyle(
          fontSize: 24,
          color: const Color(0xffbfbfbf),
        ),
        hintText: this.hintText ?? '',
        border: OutlineInputBorder(
          borderSide: BorderSide(
            width: 1.0,
            color: const Color(0xffdddddd),
          ),
          borderRadius: const BorderRadius.all(const Radius.circular(10.0)),
        ),
      ),
    );
  }
}
