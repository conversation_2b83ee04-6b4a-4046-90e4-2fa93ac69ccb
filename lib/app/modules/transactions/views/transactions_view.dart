import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/blank.dart';
import 'package:guests/app/components/filter_and_search.dart';
import 'package:guests/app/components/filter_button.dart';
import 'package:guests/app/components/primary_background.dart';
import 'package:guests/app/components/order_item.dart';
import 'package:guests/app/models/order_model.dart';
import 'package:guests/app/modules/transactions/controllers/transactions_controller.dart';
import 'package:guests/app/routes/app_pages.dart';
import 'package:guests/constants.dart';
import 'package:guests/enums.dart';
import 'package:guests/extension.dart';

class TransactionsView extends GetView<TransactionsController> {
  final _actions = [
    MapEntry(
      '全部訂單',
      <num>[],
    ),
    MapEntry(
      '成立的訂單',
      <num>[
        OrderStatus.Padding.index,
        OrderStatus.Accepted.index,
        OrderStatus.Completed.index,
      ],
    ),
    MapEntry(
      '取消的訂單',
      <num>[
        OrderStatus.Canceled.index,
        OrderStatus.Exception.index,
        OrderStatus.Refunded.index,
      ],
    ),
  ];

  TransactionsView({
    Key key,
  }) : super(key: key);

  void _onItemPressed(Order order) {
    Get.toNamed(
      Routes.ORDER_DETAIL,
      parameters: {
        kKeyId: '${order.id}',
      },
    );
  }

  Widget _list() {
    final it = controller.orders;
    return ListView.builder(
      controller: controller.scroll,
      padding: const EdgeInsets.only(
        bottom: kBottomPadding,
      ),
      itemCount: it.length + (controller.filter.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < it.length) {
          final data = it.elementAt(index);
          return OrderItem(
            data: data,
            onPressed: _onItemPressed,
          );
        }
        return const Center(
          child: CircularProgressIndicator(),
        ).paddingSymmetric(vertical: 16.0);
      },
    );
  }

  Iterable<Widget> _buildActions() sync* {
    yield Container(
      constraints: BoxConstraints(
        minWidth: 300.0,
      ),
      decoration: const BoxDecoration(
        borderRadius: kBorderRadius,
        color: Colors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(_actions.length, (index) {
          final action = _actions.elementAt(index);
          return TextButton(
            onPressed: () => Get.back(result: action),
            child: Text(
              action.key ?? '',
              style: const TextStyle(
                fontSize: 20,
                color: Colors.black,
                fontWeight: FontWeight.w700,
              ),
              textAlign: TextAlign.center,
            ).paddingSymmetric(
              vertical: 8.0,
            ),
          );
        }),
      ).paddingSymmetric(
        vertical: 20.0,
      ),
    );
    yield Padding(
      padding: const EdgeInsets.symmetric(vertical: kPadding),
      child: FloatingActionButton(
        onPressed: () => Get.back(),
        backgroundColor: Colors.white,
        child: Icon(Icons.close),
      ),
    );
  }

  Future<MapEntry> _showFilterDialog() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: List.from(_buildActions(), growable: false),
    ).sheet<MapEntry>();
  }

  Future<void> _onFilterClicked() async {
    final action = await _showFilterDialog();
    if (action is MapEntry) {
      controller.filter.text = action.key;
      controller.filter.status.clear();
      controller.filter.status.addAll(action.value);
      controller.refreshFilter();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TransactionsController>(
      init: TransactionsController(
        orderProvider: Get.find(),
      ),
      builder: (controller) {
        return PrimaryBackground(
          child: Scaffold(
            backgroundColor: Colors.transparent,
            appBar: AppBar(
              actions: [
                Obx(() {
                  return FilterButton(
                    buttonText: controller.filter.text ?? '篩選',
                    onPressed: _onFilterClicked,
                  );
                }),
              ],
              brightness: Brightness.dark,
              elevation: 0.0,
              backgroundColor: Colors.transparent,
              title: Text(
                'order_history'.tr,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              centerTitle: true,
            ),
            body: DecoratedBox(
              decoration: const BoxDecoration(
                borderRadius: kTopRadius,
                gradient: const LinearGradient(
                  begin: const Alignment(0.0, -1.0),
                  end: const Alignment(0.0, 1.0),
                  colors: const [
                    const Color(0xff3e4b5a),
                    kColorBackground,
                  ],
                  stops: const [0.0, 1.0],
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: List.from(_buildBodyElements(), growable: false),
              ),
            ),
          ),
        );
      },
    );
  }

  Iterable<Widget> _buildBodyElements() sync* {
    yield FilterAndSearch(
      hintText: '請輸入訂單編號',
      tabs: [
        'today'.tr,
        'all'.tr,
      ],
      onPressed: (value) {
        final oldValue = controller.filter.dateTimeRange != null ? 0 : 1;
        if (oldValue != value) {
          final range = value == 0 ? controller.today : null;
          controller.filter.dateTimeRange = range;
          controller.refreshFilter();
        }
      },
      onChanged: (value) {
        controller.filter.keyword = value;
        controller.refreshFilter();
      },
    );
    yield Expanded(
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: kColorBackground,
        ),
        child: controller.obx(
          (state) {
            return Obx(() => _list());
            // return RefreshIndicator(
            //   onRefresh: () => controller.onRefresh(),
            //   child: Obx(() => _list()),
            //   // child: _list(),
            // );
          },
          onEmpty: Stack(
            children: [
              const Blank(),
              ListView(),
            ],
          ),
          onError: (value) {
            return const Blank();
          },
        ),
      ),
    );
  }
}
