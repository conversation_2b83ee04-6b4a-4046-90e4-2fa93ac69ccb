import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/blank.dart';
import 'package:guests/app/components/custom_scaffold.dart';
import 'package:guests/app/components/label_value.dart';
import 'package:guests/app/models/store_account_model.dart';
import 'package:guests/app/modules/account_list/controllers/account_list_controller.dart';
import 'package:guests/app/routes/app_pages.dart';
import 'package:guests/app/components/Avatar.dart';
import 'package:guests/app/components/BottomButton.dart';
import 'package:guests/constants.dart';
import 'package:guests/extension.dart';

class AccountListView extends GetView<AccountListController> {
  const AccountListView({
    Key key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      clipped: false,
      titleText: '操作員帳號設定', // TODO: i18n
      child: Stack(
        children: [
          _main(),
          Align(
            alignment: Alignment.bottomCenter,
            child: BottomButton(
              '新增操作員', // TODO: i18n
              onPressed: () => Get.toNamed(Routes.ACCOUNT_DETAIL),
            ),
          ),
        ],
      ),
    );
  }

  Widget _list() {
    return ListView.separated(
      padding: const EdgeInsets.only(
        bottom: kBottomPadding,
      ),
      itemCount: controller.accounts.length,
      separatorBuilder: (context, index) {
        return const SizedBox(
          height: 8.0,
        );
      },
      itemBuilder: (context, index) {
        final data = controller.accounts.elementAt(index);
        return _Item(
          data: data,
          onPressed: () {
            Get.toNamed(
              Routes.ACCOUNT_DETAIL,
              parameters: {
                'id': '${data.id}',
              },
            );
          },
        );
      },
    );
  }

  Iterable<Widget> _children() sync* {
    yield _searchBar();
    yield Expanded(
      child: controller.obx(
        (value) => _list(),
        onEmpty: Blank(),
      ),
    );
  }

  Widget _searchBar() {
    return TextField(
      onChanged: (value) {
        controller.keyword = value;
      },
      inputFormatters: [
        FilteringTextInputFormatter.singleLineFormatter,
      ],
      decoration: InputDecoration(
        contentPadding: EdgeInsets.zero,
        hintText: '請輸入操作員名稱或帳號',
        hintStyle: const TextStyle(
          fontSize: 16,
        ),
        fillColor: Colors.white,
        filled: true,
        prefixIcon: const Icon(
          Icons.search,
        ),
        border: OutlineInputBorder(
          borderSide: const BorderSide(
            color: const Color(0xffdddddd),
          ),
          borderRadius: kBorderRadius,
        ),
      ),
    ).paddingSymmetric(
      vertical: 8.0,
      horizontal: 8.0,
    );
  }

  Widget _main() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }
}

class _Item extends GetView<AccountListController> {
  final StoreAccount data;
  final Function onPressed;

  const _Item({
    Key key,
    @required this.data,
    @required this.onPressed,
  }) : super(key: key);

  Iterable<Widget> _children() sync* {
    yield Avatar(
      data: this.data,
    ).paddingSymmetric(
      horizontal: kPadding,
      vertical: 16.0,
    );
    yield const Divider(
      height: 1.0,
      indent: kPadding,
      endIndent: kPadding,
    );
    yield LabelValue(
      labelText: '上次登入時間  ',
      labelStyle: TextStyle(
        fontSize: 16,
        color: Colors.black,
        height: 2,
      ),
      valueText: data.displayLastLogin ?? '',
      valueStyle: TextStyle(
        fontFamily: 'Helvetica Neue',
        fontSize: 16,
        color: const Color(0xff3e4b5a),
        height: 2,
      ),
    ).paddingSymmetric(
      vertical: 12.0,
      horizontal: kPadding,
    );
    yield TextButton.icon(
      onPressed: this.onPressed,
      icon: Icon(
        Icons.edit,
        color: const Color(0xFF3E4B5A),
      ),
      label: const Text(
        '編輯', // TODO: i18n
        style: const TextStyle(
          fontSize: 14,
          color: const Color(0xff3e4b5a),
        ),
        textAlign: TextAlign.left,
      ),
      style: ButtonStyle(
        side: MaterialStateProperty.all(BorderSide(
          width: 1.0,
          color: const Color(0xffdbdbea),
        )),
        padding: MaterialStateProperty.all(EdgeInsets.zero),
        backgroundColor: MaterialStateProperty.all(kColorBackground),
        shape: MaterialStateProperty.all(StadiumBorder()),
        minimumSize: MaterialStateProperty.all(Size(double.infinity, 30.0)),
      ),
    ).paddingSymmetric(
      horizontal: kPadding,
    );
    yield const SizedBox(
      height: 12.0,
    );
  }

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _children().toList(),
      ),
    );
  }
}
