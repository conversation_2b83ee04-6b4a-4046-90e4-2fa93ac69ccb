import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:guests/app/modules/home/<USER>/home_controller.dart';
import 'package:guests/app/modules/settings/views/settings_view.dart';
import 'package:guests/app/modules/transactions/views/transactions_view.dart';
import 'package:guests/app/routes/app_pages.dart';
import 'package:guests/app/components/CircleButton.dart';
import 'package:guests/extension.dart';

class HomeView extends GetView<HomeController> {
  Widget _slave() => _createPage(controller.homeMenu);

  static Widget _createPage(HomeMenu menu) {
    switch (menu) {
      case HomeMenu.list:
        return TransactionsView();
      case HomeMenu.settings:
        return SettingsView();
      default:
        return SizedBox();
    }
  }

  Widget _master() {
    return Scaffold(
      body: Obx(() => _slave()),
      floatingActionButton: CircleButton(
        onPressed: () {
          final uri = Uri(
            path: Routes.CREATE_ORDER,
          );
          Get.toNamed(uri.toString());
        },
        icon: SvgPicture.asset('assets/images/icon_pos.svg'),
        text: Text(
          '結帳',
          style: TextStyle(
            fontFamily: 'PingFang TC',
            fontSize: 15,
            color: const Color(0xfff7f7f7),
          ),
          textAlign: TextAlign.center,
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: BottomAppBar(
        color: Color(0xFFF7F7F7),
        shape: CircularNotchedRectangle(),
        child: Container(
          child: Obx(() {
            print('${controller.homeMenu}');
            final children = <Widget>[];
            // 已移除會員功能
            // children.addIf(
            //   false,
            //   _Button(
            //     text: '我的會員',
            //     icon: 'assets/images/icon_fans.svg',
            //     checked: HomeMenu.fans == controller.homeMenu,
            //     onPressed: () => controller.homeMenu = HomeMenu.fans,
            //   ),
            // );
            children.addIf(
              true,
              _Button(
                text: '消費紀錄',
                icon: 'assets/images/icon_list.svg',
                checked: HomeMenu.list == controller.homeMenu,
                onPressed: () => controller.homeMenu = HomeMenu.list,
              ),
            );
            children.addIf(true, SizedBox());
            // 已移除電商訂單功能
            // children.addIf(
            //   false,
            //   _Button(
            //     text: '電商訂單',
            //     icon: 'assets/images/icon_order.svg',
            //     checked: HomeMenu.order == controller.homeMenu,
            //     onPressed: () => controller.homeMenu = HomeMenu.order,
            //   ),
            // );
            children.addIf(
              true,
              _Button(
                text: '設定',
                icon: 'assets/images/icon_gear.svg',
                checked: HomeMenu.settings == controller.homeMenu,
                onPressed: () => controller.homeMenu = HomeMenu.settings,
              ),
            );
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: children,
            );
          }),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    initializeController().then((value) {
      controller.onPostFrame();
    });
    return controller.obx(
      (state) => _master(),
      onLoading: Center(
        child: CircularProgressIndicator(),
      ),
      onError: (error) => Center(
        child: Icon(
          Icons.error,
          color: Colors.red,
        ),
      ),
    );
  }
}

class _Button extends StatelessWidget {
  final String text;
  final String icon;
  final Function onPressed;
  final bool checked;

  const _Button({
    Key key,
    this.text = '',
    this.icon = '',
    this.onPressed,
    this.checked = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var color = this.checked ? Get.theme.accentColor : const Color(0xff333333);
    return TextButton(
      onPressed: this.onPressed,
      // child: Tab(
      //   iconMargin: EdgeInsets.zero,
      //   icon: SvgPicture.asset(
      //     this.icon,
      //     color: color,
      //   ),
      //   child: Text(
      //     text,
      //     style: TextStyle(
      //       fontFamily: 'PingFang TC',
      //       fontSize: 12,
      //       color: color,
      //     ),
      //     textAlign: TextAlign.center,
      //   ),
      // ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            this.icon,
            color: color,
          ),
          Text(
            text,
            style: TextStyle(
              fontFamily: 'PingFang TC',
              fontSize: 12,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
