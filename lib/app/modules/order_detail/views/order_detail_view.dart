import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:guests/app/components/blank.dart';
import 'package:guests/app/components/custom_list_tile.dart';
import 'package:guests/app/components/custom_scaffold.dart';
import 'package:guests/app/components/dialog_custom.dart';
import 'package:guests/app/components/future_progress.dart';
import 'package:guests/app/components/invoice_page.dart';
import 'package:guests/app/components/label_value.dart';
import 'package:guests/app/models/order_detail_model.dart';
import 'package:guests/app/modules/order_detail/controllers/order_detail_controller.dart';
import 'package:guests/constants.dart';
import 'package:guests/enums.dart';
import 'package:guests/extension.dart';
import 'package:guests/ok_colors.dart';
import 'package:screenshot/screenshot.dart';

class OrderDetailView extends GetView<OrderDetailController> {
  const OrderDetailView({
    Key key,
  }) : super(key: key);

  Iterable<Widget> _children() sync* {
    // 發票截圖
    yield Obx(() {
      if (controller.invoiceVisible) {
        final ret = Screenshot(
          controller: controller.screenshotController,
          child: InvoicePage(
            controller.cached.toInvoice(),
            storeName: controller.prefProvider.storeName,
            productName: controller.productName,
          ),
        );
        controller.widgetUpdater.complete();
        return ret;
      }
      return const SizedBox.shrink();
    });
    // 國防布
    yield ColoredBox(
      color: OkColors.background,
      child: const SizedBox.expand(),
    );
    // 使用者實際看到的預覽介面
    yield _body();
  }

  Widget _body() {
    return DecoratedBox(
      decoration: const BoxDecoration(
        color: OkColors.grayF7,
        borderRadius: kTopRadius,
      ),
      child: controller.obx(
        (state) => Column(
          children: [
            _Header(
              data: controller.cached,
            ),
            Expanded(
              child: _Page(
                data: controller.cached,
              ),
            ),
          ],
        ),
        onEmpty: const Blank(),
        onError: (error) => const Blank(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      titleText: 'OKpay',
      clipped: true,
      actions: _actions(),
      child: Stack(
        alignment: Alignment.topCenter,
        children: _children().toList(growable: false),
      ),
    );
  }

  Iterable<Widget> _actions() sync* {
    // yield TextButton(
    //   onPressed: _onInvoicePressed,
    //   child: Text("補上傳發票"),
    // );
    // yield TextButton(
    //   onPressed: _onCancelPressed,
    //   child: Text("補作廢發票"),
    // );
  }

  Future<void> _onCancelPressed() async {
    final invoiceNumber = await FutureProgress(
      future: controller.uploadCancel(),
    ).dialog();
    kLogger.d('[OrderDetailView] Cancel invoiceNumber: ' + invoiceNumber);
  }

  Future<void> _onInvoicePressed() async {
    final invoiceNumber = await FutureProgress(
      future: controller.uploadInvoice(),
    ).dialog();
    kLogger.d('[OrderDetailView] Upload invoiceNumber: ' + invoiceNumber);
  }
}

class _Header extends GetView<OrderDetailController> {
  final OrderDetail data;

  const _Header({
    Key key,
    @required this.data,
  }) : super(key: key);

  Future<void> _showDialog() async {
    controller.orderStatus.value = controller.cached.status;
    final res = await DialogCustom.showConfirm(
      titleText: '修改訂單狀態',
      content: Obx(() => _radioDialog()),
      icon: DialogIcon.none,
    );
    if (Button.Positive == res && controller.needToRefund) {
      FutureProgress.show(
        future: controller.refundOrder(),
      ).then(
        (value) {
          controller.onRefresh();
        },
        onError: (error) {
          kLogger.e(error);
        },
      );
    }
  }

  Widget _radioDialog() {
    Iterable<Widget> children<T extends num>() sync* {
      yield RadioListTile<T>(
        onChanged: controller.orderStatus,
        activeColor: OkColors.primary,
        value: OrderStatus.Completed.index as T,
        groupValue: controller.orderStatus.value,
        title: Text(
          '訂單完成',
          style: TextStyle(
            fontSize: 20,
            color: OrderStatus.Completed.index == controller.orderStatus.value
                ? OkColors.primary
                : Colors.black,
          ),
          textAlign: TextAlign.left,
        ),
      );
      yield RadioListTile<T>(
        onChanged: controller.orderStatus,
        activeColor: OkColors.primary,
        value: OrderStatus.Refunded.index as T,
        groupValue: controller.orderStatus.value,
        title: Text(
          // '退款並作廢發票',
          // this.controller.displayRefound ?? '',
          controller.displayRefound ?? '',
          style: TextStyle(
            fontSize: 20,
            color: OrderStatus.Refunded.index == controller.orderStatus.value
                ? OkColors.primary
                : Colors.black,
          ),
          textAlign: TextAlign.left,
        ),
      );
    }

    return IntrinsicWidth(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: children().toList(growable: false),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: OkColors.grayF7,
      ),
      height: Constants.buttonHeight,
      padding: const EdgeInsets.symmetric(
        horizontal: Constants.paddingHorizontal,
      ),
      alignment: Alignment.centerRight,
      child: Visibility(
        visible: data.isCompleted,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            primary: OkColors.grayF0,
            padding: kChipPadding,
            side: const BorderSide(
              width: 1.0,
              color: Colors.white,
            ),
            shape: const StadiumBorder(),
            minimumSize: Size.zero,
          ),
          onPressed: _showDialog,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: const [
              SizedBox(width: 8.0),
              Text(
                '訂單完成',
                style: TextStyle(
                  fontSize: 16,
                  color: OkColors.gray33,
                ),
                textAlign: TextAlign.center,
              ),
              Icon(
                Icons.expand_more,
                color: OkColors.gray33,
              ),
            ],
          ),
        ),
        replacement: Text(
          data.displayStatus ?? '',
          style: TextStyle(
            fontSize: 16,
            color: data.displayStatusColor,
          ),
          textAlign: TextAlign.left,
        ),
      ),
    );
  }
}

class _Page extends GetView<OrderDetailController> {
  final OrderDetail data;

  const _Page({
    Key key,
    @required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ColoredBox(
            color: Colors.white,
            child: _Page01(
              data: data,
            ),
          ),
          ColoredBox(
            color: const Color(0xffe2e2e2),
            child: _Page02(
              data: data,
            ),
          ),
        ],
      ),
    );
  }
}

class _Page01 extends GetView<OrderDetailController> {
  final OrderDetail data;

  const _Page01({
    Key key,
    @required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: _children().toList(growable: false),
    );
  }

  Widget _invoice() {
    Iterable<Widget> children() sync* {
      yield LabelValue(
        labelText: '發票號碼：',
        valueText: controller.cached?.invoice?.number ?? '',
      );
      if (controller.cached.invoice != null) {
        yield const SizedBox(width: 4.0);
        if (controller.cached.isCompleted == true) {
          yield ElevatedButton(
            style: ElevatedButton.styleFrom(
              primary: const Color(0xff3e4b5a),
              padding: kChipPadding,
              shape: const StadiumBorder(),
              side: const BorderSide(
                width: 1.0,
                color: Colors.white,
              ),
              minimumSize: Size.zero,
            ),
            onPressed: controller.print,
            child: const Text(
              '補印發票',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          );
        } else {
          yield Text(
            '作廢',
            style: const TextStyle(
              fontSize: 16.0,
              color: Colors.black,
            ),
          );
        }
      }
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: children().toList(growable: false),
    );
  }

  Iterable<Widget> _children() sync* {
    yield const SizedBox(height: kDefaultPadding);
    yield CustomListTile(
      padding: Constants.paddingContent,
      leadingText: '訂單編號：',
      titleText: data.orderNumber ?? '',
      trailText: data.displayDateTimeMdHm ?? '',
    );
    yield _invoice().paddingSymmetric(horizontal: Constants.paddingHorizontal);
    yield CustomListTile(
      padding: Constants.paddingContent,
      leadingText: '統一編號：',
      titleText: controller.cached?.invoice?.vatNumber,
    );
    yield const SizedBox(height: kDefaultPadding);
  }
}

class _Page02 extends StatelessWidget {
  final OrderDetail data;

  const _Page02({
    Key key,
    @required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(
          height: 16.0,
        ),
        Text.rich(
          TextSpan(
            style: TextStyle(
              fontSize: 16,
              color: kColorPrimary,
            ),
            children: [
              TextSpan(
                text: '1 ',
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                ),
              ),
              TextSpan(
                text: '項，',
                style: const TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                ),
              ),
              TextSpan(
                text: '\$${(this.data.total ?? 0).currency}',
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                ),
              ),
              TextSpan(
                text: ' 元',
                style: const TextStyle(
                  color: const Color(0xff222222),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          textHeightBehavior:
              TextHeightBehavior(applyHeightToFirstAscent: false),
          textAlign: TextAlign.left,
        ).paddingSymmetric(
          horizontal: kPadding,
        ),
        const SizedBox(
          height: 16.0,
        ),
        SizedBox(
          width: double.infinity,
          child: SvgPicture.asset(
            'assets/images/dot_line_02.svg',
            fit: BoxFit.fitWidth,
          ),
        ),
        const SizedBox(
          height: 22.0,
        ),
        Row(
          children: [
            Expanded(
              child: Builder(
                builder: (context) {
                  final ls = this.data.orderItems;
                  final v = ls.isEmpty ? '0' : ls.first?.finalPrice?.currency;
                  return LabelValue(
                    labelText: '商品小計: ',
                    // valueText: '0',
                    valueText: v ?? '',
                  );
                },
              ),
            ),
            Expanded(
              child: LabelValue(
                labelText: '服務費(0%): ',
                valueText: '0',
              ),
            ),
          ],
        ).paddingSymmetric(
          horizontal: kPadding,
        ),
        const SizedBox(
          height: 18.0,
        ),
        Row(
          children: [
            Expanded(
              child: Builder(
                builder: (context) {
                  final ls = this.data.orderDiscount;
                  final v =
                      ls.isEmpty ? '0' : ls.first?.discountPrice?.currency;
                  return LabelValue(
                    labelText: '現場折價: ',
                    // valueText: '0',
                    valueText: v ?? '',
                  );
                },
              ),
            ),
            Expanded(
              child: LabelValue(
                labelText: '額外費用: ',
                // valueText: '0',
                valueText: this.data.additionalCharges.currency ?? '',
              ),
            ),
          ],
        ).paddingSymmetric(
          horizontal: kPadding,
        ),
        const SizedBox(
          height: 22.0,
        ),
        SizedBox(
          width: double.infinity,
          child: SvgPicture.asset(
            'assets/images/dot_line_02.svg',
            fit: BoxFit.fitWidth,
          ),
        ),
        const SizedBox(
          height: 18.0,
        ),
        LabelValue(
          labelText: '商品總價: ',
          // valueText: '\$${(this.data.total ?? 0).currency}',
          valueText: '\$${(this.data?.orderPayment?.total ?? 0).currency}',
          labelStyle: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: Colors.black,
          ),
          valueStyle: const TextStyle(
            fontSize: 20,
            color: kColorPrimary,
            fontWeight: FontWeight.w700,
          ),
        ).paddingSymmetric(
          horizontal: kPadding,
        ),
        const SizedBox(
          height: kDefaultPadding,
        ),
        Row(
          children: [
            Expanded(
              child: LabelValue(
                labelText: '實收: ',
                // valueText: '0',
                valueText: this.data.orderPayment?.paid?.currency ?? '',
              ),
            ),
            Expanded(
              child: LabelValue(
                labelText: '找零: ',
                // valueText: '0',
                valueText: this.data.orderPayment?.change?.currency ?? '',
              ),
            ),
          ],
        ).paddingSymmetric(
          horizontal: kPadding,
        ),
        const SizedBox(
          height: 18.0,
        ),
        SizedBox(
          width: double.infinity,
          child: SvgPicture.asset(
            'assets/images/dot_line_02.svg',
            fit: BoxFit.fitWidth,
          ),
        ),
        const SizedBox(
          height: kDefaultPadding,
        ),
        SizedBox(
          height: kDefaultPadding,
        ),
      ],
    );
  }
}
